import socket
import asyncio
import logging
import config

# Global flag to track when FFmpeg has connected to MediaMTX
ffmpeg_connected_to_mediamtx = asyncio.Event()

def get_local_ip():
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    try:
        s.connect(('**************', 1))
        IP = s.getsockname()[0]
    except Exception:
        IP = '127.0.0.1'
    finally:
        s.close()
    return IP

async def log_process_output(pipe, logger_func, prefix=""):
    """Log process output with specific detection for FFmpeg/MediaMTX connection issues"""
    while True:
        try:
            line = await pipe.readline()
            if not line:
                break
            line_str = line.decode().strip()
            logger_func(f"{prefix}{line_str}")
            
            # CRITICAL: Detect FFmpeg RTSP connection failures to MediaMTX
            if "ffmpeg" in prefix.lower():
                # Audio-specific error detection
                if "audio" in line_str.lower():
                    if "codec" in line_str.lower() and ("error" in line_str.lower() or "failed" in line_str.lower()):
                        logging.critical(f"🔊 FFMPEG AUDIO CODEC ERROR: {line_str}")
                    elif "buffer" in line_str.lower() and ("full" in line_str.lower() or "overflow" in line_str.lower()):
                        logging.critical(f"🔊 FFMPEG AUDIO BUFFER ISSUE: {line_str}")
                    elif "format" in line_str.lower() and ("not supported" in line_str.lower() or "invalid" in line_str.lower()):
                        logging.critical(f"🔊 FFMPEG AUDIO FORMAT ERROR: {line_str}")
                    elif "sample rate" in line_str.lower() or "channels" in line_str.lower():
                        logging.warning(f"🔊 FFMPEG AUDIO CONFIG: {line_str}")
                    elif "sync" in line_str.lower() or "timestamp" in line_str.lower():
                        logging.warning(f"🔊 FFMPEG AUDIO SYNC: {line_str}")
                        
                # FIFO-specific error detection
                if "fifo" in line_str.lower() or config.audio_fifo_path in line_str:
                    if "error" in line_str.lower() or "failed" in line_str.lower():
                        logging.critical(f"🔊 FFMPEG AUDIO FIFO ERROR: {line_str}")
                    elif "timeout" in line_str.lower() or "blocking" in line_str.lower():
                        logging.critical(f"🔊 FFMPEG AUDIO FIFO BLOCKED: {line_str}")
                    elif "no such file" in line_str.lower():
                        logging.critical(f"🔊 FFMPEG AUDIO FIFO MISSING: {line_str}")
                        
                # Thread/processing errors
                if ("thread" in line_str.lower() or "decoder" in line_str.lower()) and "audio" in line_str.lower():
                    if "error" in line_str.lower() or "failed" in line_str.lower() or "hang" in line_str.lower():
                        logging.critical(f"🔊 FFMPEG AUDIO THREAD ERROR: {line_str}")
                        
                # General connection/pipe errors
                if "rtsp" in line_str.lower() and ("connection" in line_str.lower() or "failed" in line_str.lower() or "refused" in line_str.lower()):
                    logging.critical(f"🔥 FFMPEG RTSP CONNECTION FAILURE: {line_str}")
                    logging.critical("FFmpeg cannot connect to MediaMTX RTSP endpoint!")
                elif "no such file or directory" in line_str.lower():
                    logging.critical(f"🔥 FFMPEG FILE/PATH ERROR: {line_str}")
                elif "broken pipe" in line_str.lower():
                    logging.critical(f"🔥 FFMPEG BROKEN PIPE: {line_str}")
                elif "connection reset" in line_str.lower():
                    logging.critical(f"🔥 FFMPEG CONNECTION RESET: {line_str}")
                elif "timeout" in line_str.lower():
                    logging.critical(f"🔥 FFMPEG TIMEOUT: {line_str}")
                    
                # Track FFmpeg input processing status
                if "input #" in line_str.lower():
                    if "audio" in line_str.lower():
                        logging.info(f"🔊 FFMPEG AUDIO INPUT: {line_str}")
                    elif "video" in line_str.lower():
                        logging.info(f"📹 FFMPEG VIDEO INPUT: {line_str}")
                    
        except Exception:
            break


async def log_mediamtx_output(stream, log_func, prefix=""):
    """Log MediaMTX output with specific detection for /doorbell path issues"""
    while True:
        try:
            line = await stream.readline()
            if not line:
                break
            line_str = line.decode().strip()
            log_func(f"{prefix}{line_str}")
            
            # Detect MediaMTX startup completion
            if "RTSP server at" in line_str or "rtsp server listener opened" in line_str:
                logging.info("✅ MediaMTX RTSP server is ready")
            elif "started" in line_str and "listener" in line_str:
                logging.info("✅ MediaMTX listener started")
            
            # CRITICAL: Detect MediaMTX path errors that may cause FFmpeg crashes
            if "path 'doorbell'" in line_str:
                if "does not exist" in line_str or "not found" in line_str or "no such path" in line_str:
                    logging.critical(f"🔥 MEDIAMTX PATH ERROR: {line_str}")
                    logging.critical("🔥 This WILL cause FFmpeg to crash! MediaMTX cannot find /doorbell path")
                    logging.critical("🔥 Check mediamtx.yml configuration for doorbell path!")
                elif "is publishing" in line_str:
                    ffmpeg_connected_to_mediamtx.set()
                    logging.info("✅ FFmpeg successfully connected to MediaMTX - stream is ready")
                elif "is not publishing" in line_str:
                    ffmpeg_connected_to_mediamtx.clear()
                    logging.info("❌ FFmpeg disconnected from MediaMTX - stream is not ready")
                elif "failed" in line_str or "error" in line_str:
                    logging.critical(f"🔥 MEDIAMTX DOORBELL ERROR: {line_str}")
            
            # Detect RTSP connection issues
            if "rtsp" in line_str.lower() and ("error" in line_str.lower() or "failed" in line_str.lower() or "connection" in line_str.lower()):
                logging.critical(f"🔥 RTSP CONNECTION ERROR: {line_str}")
                
            # Detect publisher connection issues  
            if "publisher" in line_str and ("disconnected" in line_str or "failed" in line_str or "error" in line_str):
                logging.critical(f"🔥 PUBLISHER CONNECTION ERROR: {line_str}")
                
            # Detect any other critical errors
            if "fatal" in line_str.lower() or "panic" in line_str.lower():
                logging.critical(f"🔥 MEDIAMTX FATAL ERROR: {line_str}")
                
        except Exception:
            break