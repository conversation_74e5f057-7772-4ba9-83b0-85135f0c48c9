import asyncio
import logging
import subprocess
import signal
import psutil
import os
from aiohttp import web

import config
from utils import get_local_ip, log_process_output, log_mediamtx_output, ffmpeg_connected_to_mediamtx
from onvif_service import handle_onvif, handle_snapshot, start_onvif_discovery
from http_handlers import handle_start, handle_start_twoway, handle_stop, handle_doorbell_press_with_image, handle_update_snapshot, handle_audio_input, handle_audio_diagnostics
from media_pipeline import stream_generator, audio_stream_generator, buffer_monitor


async def start_ffmpeg(use_software_encoder=False):
    """Start or restart the FFmpeg process"""
    # Use the audio FIFO path from config (created in main())
    audio_fifo = config.audio_fifo_path
    
    if use_software_encoder:
        logging.warning("Using software encoder (libx264) - VAAPI may have failed")
        command = [
            'ffmpeg', '-y',
            '-stats',
            # Low-latency input flags
            '-fflags', 'nobuffer',
            '-probesize', '32',
            '-analyzeduration', '0',
            # Video input from pipe:0 (stdin)
            '-f', 'rawvideo',
            '-pix_fmt', 'bgr24',
            '-s', f'{config.VIDEO_WIDTH}x{config.VIDEO_HEIGHT}',
            '-r', str(config.DUMMY_FPS),
            '-thread_queue_size', '64',
            '-i', 'pipe:0',
            # Audio input from FIFO (live audio) or fallback to dummy
            '-f', 's16le',
            '-ar', '8000',
            '-ac', '1',
            # Reduced thread queue to prevent blocking
            '-thread_queue_size', '64',
            # Add audio buffering to prevent FIFO blockage
            '-buffer_size', '32768',
            '-i', audio_fifo,
            # Video encoding with SOFTWARE encoder
            '-c:v', 'libx264',
            '-preset', 'ultrafast',
            '-tune', 'zerolatency',
            '-b:v', '1000k',
            '-maxrate', '1000k',
            '-g', '24',
            # Audio encoding - keep same sample rate to avoid conversion issues
            '-c:a', 'aac',
            '-ar', '8000',
            '-b:a', '32k',
            # Audio processing options to prevent blocking
            '-avoid_negative_ts', 'make_zero',
            '-fflags', '+genpts',
            # Map inputs
            '-map', '0:v:0',
            '-map', '1:a:0',
            '-f', 'rtsp',
            config.MEDIAMTX_URL
        ]
    else:
        command = [
            'ffmpeg', '-y',
            '-stats',
            # Low-latency input flags
            '-fflags', 'nobuffer',
            '-probesize', '32',
            '-analyzeduration', '0',
            '-vaapi_device', '/dev/dri/renderD128',
            # Video input from pipe:0 (stdin)
            '-f', 'rawvideo',
            '-pix_fmt', 'bgr24',
            '-s', f'{config.VIDEO_WIDTH}x{config.VIDEO_HEIGHT}',
            '-r', str(config.DUMMY_FPS),
            '-thread_queue_size', '64',
            '-i', 'pipe:0',
            # Audio input from FIFO (live audio) or fallback to dummy
            '-f', 's16le',
            '-ar', '8000',
            '-ac', '1',
            # Reduced thread queue to prevent blocking
            '-thread_queue_size', '64',
            # Add audio buffering to prevent FIFO blockage
            '-buffer_size', '32768',
            '-i', audio_fifo,
            # Video encoding with HARDWARE encoder
            '-c:v', 'h264_vaapi',
            '-vf', 'format=nv12,hwupload',
            '-preset', 'ultrafast',
            '-tune', 'zerolatency',
            '-b:v', '1000k',
            '-maxrate', '1000k',
            '-g', '24',
            # Audio encoding - keep same sample rate to avoid conversion issues
            '-c:a', 'aac',
            '-ar', '8000',
            '-b:a', '32k',
            # Audio processing options to prevent blocking
            '-avoid_negative_ts', 'make_zero',
            '-fflags', '+genpts',
            # Map inputs
            '-map', '0:v:0',
            '-map', '1:a:0',
            '-f', 'rtsp',
            config.MEDIAMTX_URL
        ]

    encoder_type = "software (libx264)" if use_software_encoder else "hardware (VAAPI)"
    logging.info(f"Starting FFmpeg process with {encoder_type}: {' '.join(command)}")
    
    # CRITICAL: Check if MediaMTX is ready to receive streams
    if config.mediamtx_process is None or config.mediamtx_process.returncode is not None:
        logging.critical("🔥 CANNOT START FFMPEG: MediaMTX process is not running!")
        return False
    
    # Wait a moment for MediaMTX to be fully initialized
    await asyncio.sleep(0.5)
    
    try:
        config.ffmpeg_process = await asyncio.create_subprocess_exec(*command, stdin=subprocess.PIPE, stderr=subprocess.PIPE)
        asyncio.create_task(log_process_output(config.ffmpeg_process.stderr, logging.error, prefix="[FFMPEG] "))
        logging.info(f"✅ FFmpeg process started with PID: {config.ffmpeg_process.pid}")
        
        # Give FFmpeg a moment to attempt connection to MediaMTX
        await asyncio.sleep(1)
        
        # Check if FFmpeg is still running after connection attempt
        if config.ffmpeg_process.returncode is not None:
            logging.critical(f"🔥 FFMPEG DIED IMMEDIATELY: return code {config.ffmpeg_process.returncode}")
            logging.critical("This suggests MediaMTX /doorbell path is not ready or connection failed")
            return False
            
        return True
    except Exception as e:
        logging.critical(f"🔥 FAILED TO START FFMPEG: {e}")
        return False


async def ffmpeg_monitor():
    """Monitor FFmpeg process and restart if needed"""
    while True:
        try:
            # Check if FFmpeg restart has been requested due to stdin issues
            if config.ffmpeg_restart_requested.is_set():
                logging.warning("FFmpeg restart requested due to stdin timeout/issues - forcing restart")
                config.ffmpeg_restart_requested.clear()
                
                # Terminate current FFmpeg process if it exists
                if config.ffmpeg_process and config.ffmpeg_process.returncode is None:
                    try:
                        config.ffmpeg_process.terminate()
                        # Wait a moment for graceful termination
                        await asyncio.sleep(1)
                        if config.ffmpeg_process.returncode is None:
                            # Force kill if still running
                            config.ffmpeg_process.kill()
                            logging.warning("Forced kill of unresponsive FFmpeg process")
                    except Exception as e:
                        logging.error(f"Error terminating FFmpeg process: {e}")
                
                # Clear the process reference to trigger restart logic
                config.ffmpeg_process = None
                # Reset timeout counter since we're restarting
                config.ffmpeg_stdin_timeout_count = 0
            
            if config.ffmpeg_process is None or config.ffmpeg_process.returncode is not None:
                if config.ffmpeg_process and config.ffmpeg_process.returncode is not None:
                    logging.error(f"FFmpeg process died with return code: {config.ffmpeg_process.returncode}")
                    # Log additional context about why FFmpeg died
                    if config.ffmpeg_process.returncode == -9:
                        logging.error("FFmpeg killed by SIGKILL - likely OOM killer or system memory pressure")
                    elif config.ffmpeg_process.returncode == -15:
                        logging.error("FFmpeg killed by SIGTERM - likely terminated by system or our code")
                    elif config.ffmpeg_process.returncode == 1:
                        logging.error("FFmpeg exited with error - likely input/output/codec issues")
                    else:
                        logging.error(f"FFmpeg exited with unknown return code: {config.ffmpeg_process.returncode}")
                else:
                    logging.warning("FFmpeg process died. Attempting to restart...")
                # Clear live flag to prevent issues during restart
                config.is_live.clear()
                config.is_audio_live.clear()
                
                # CRITICAL: Clear all queues and buffers aggressively to prevent memory leak
                logging.critical("FFmpeg died - clearing all queues and buffers to prevent memory leak")
                try:
                    # Monitor and clear video queue
                    video_queue_size = config.live_frame_queue.qsize()
                    video_frames_cleared = 0
                    while not config.live_frame_queue.empty():
                        config.live_frame_queue.get_nowait()
                        config.live_frame_queue.task_done()
                        video_frames_cleared += 1
                    
                    # Monitor and clear audio queues
                    audio_queue_size = config.live_audio_queue.qsize()
                    audio_frames_cleared = 0
                    while not config.live_audio_queue.empty():
                        config.live_audio_queue.get_nowait()
                        config.live_audio_queue.task_done()
                        audio_frames_cleared += 1
                    
                    # Clear ONVIF audio queue if it exists
                    onvif_audio_cleared = 0
                    if hasattr(config, 'onvif_audio_queue'):
                        while not config.onvif_audio_queue.empty():
                            config.onvif_audio_queue.get_nowait()
                            onvif_audio_cleared += 1
                    
                    logging.critical(f"Buffer cleanup - Video queue: {video_queue_size} -> cleared {video_frames_cleared}")
                    logging.critical(f"Buffer cleanup - Audio queue: {audio_queue_size} -> cleared {audio_frames_cleared}")
                    logging.critical(f"Buffer cleanup - ONVIF audio queue: cleared {onvif_audio_cleared}")
                    
                    # Reset any accumulating counters to prevent memory growth
                    if hasattr(config, 'ffmpeg_stdin_timeout_count'):
                        config.ffmpeg_stdin_timeout_count = 0
                        logging.critical(f"Buffer cleanup - Reset stdin timeout counter")
                    
                    # Reset health tracking counters for fresh start
                    if hasattr(config, 'ffmpeg_stdin_write_success_count'):
                        config.ffmpeg_stdin_write_success_count = 0
                        config.ffmpeg_stdin_write_failure_count = 0
                        config.ffmpeg_audio_fifo_write_success_count = 0
                        config.ffmpeg_audio_fifo_write_failure_count = 0
                        logging.critical(f"Buffer cleanup - Reset FFmpeg health tracking counters")
                    
                    # Force garbage collection to free memory immediately
                    import gc
                    collected = gc.collect()
                    logging.critical(f"Buffer cleanup - Forced GC collected {collected} objects")
                    
                    # Log memory usage after cleanup
                    import psutil
                    memory_mb = psutil.Process().memory_info().rss / (1024 * 1024)
                    logging.critical(f"Buffer cleanup - Memory usage after cleanup: {memory_mb:.1f}MB")
                    
                except Exception as e:
                    logging.error(f"Error clearing queues after FFmpeg death: {e}")
                
                # Wait a moment before restart
                await asyncio.sleep(2)
                
                # Try hardware encoder first, fallback to software if it fails
                if not await start_ffmpeg(use_software_encoder=False):
                    logging.error("Failed to restart FFmpeg with hardware encoder, trying software encoder...")
                    await asyncio.sleep(1)
                    if not await start_ffmpeg(use_software_encoder=True):
                        logging.error("Failed to restart FFmpeg with both hardware and software encoders. Waiting before retry...")
                        await asyncio.sleep(5)
                        continue
            else:
                # Check if process is actually responsive
                try:
                    # For asyncio subprocess, just check if returncode is still None
                    if config.ffmpeg_process.returncode is not None:
                        logging.error(f"FFmpeg process died with return code: {config.ffmpeg_process.returncode}")
                        continue  # This will restart FFmpeg in the next iteration
                    else:
                        # Additional check: verify process is actually alive using psutil
                        import psutil
                        try:
                            process = psutil.Process(config.ffmpeg_process.pid)
                            if not process.is_running():
                                logging.error(f"FFmpeg process PID {config.ffmpeg_process.pid} is not running (zombie/dead)")
                                config.ffmpeg_process = None
                                continue
                            elif process.status() == psutil.STATUS_ZOMBIE:
                                logging.error(f"FFmpeg process PID {config.ffmpeg_process.pid} is in zombie state")
                                config.ffmpeg_process = None
                                continue
                            else:
                                # Enhanced health checks for unresponsive FFmpeg
                                cpu_percent = process.cpu_percent(interval=None)
                                memory_mb = process.memory_info().rss / (1024 * 1024)
                                num_threads = process.num_threads()
                                
                                # Get detailed thread information
                                threads_info = []
                                hung_threads = 0
                                try:
                                    for thread in process.threads():
                                        thread_info = {
                                            'id': thread.id,
                                            'user_time': thread.user_time,
                                            'system_time': thread.system_time
                                        }
                                        threads_info.append(thread_info)
                                        
                                        # Detect potentially hung threads (very low CPU time relative to others)
                                        total_time = thread.user_time + thread.system_time
                                        if len(threads_info) > 1 and total_time < 0.1:  # Less than 0.1s total
                                            hung_threads += 1
                                except Exception as e:
                                    logging.debug(f"Could not get detailed thread info: {e}")
                                
                                # Check file descriptors to detect FIFO issues
                                open_fds = 0
                                fifo_fd_status = "unknown"
                                try:
                                    open_fds = process.num_fds()
                                    # Try to find our audio FIFO in the open files
                                    for fd in process.open_files():
                                        if hasattr(config, 'audio_fifo_path') and config.audio_fifo_path in fd.path:
                                            fifo_fd_status = f"open:{fd.fd}"
                                            break
                                    else:
                                        if hasattr(config, 'audio_fifo_path'):
                                            fifo_fd_status = "not_found"
                                except Exception as e:
                                    logging.debug(f"Could not get file descriptor info: {e}")
                                
                                # Log FFmpeg health every 30 seconds
                                import time
                                current_time = time.time()
                                if not hasattr(config, '_last_ffmpeg_health_log'):
                                    config._last_ffmpeg_health_log = 0
                                    
                                log_now = current_time - config._last_ffmpeg_health_log >= 30
                                
                                # Also log immediately if we detect potential issues
                                if hung_threads > 0 or fifo_fd_status == "not_found":
                                    log_now = True
                                    
                                if log_now:
                                    logging.info(f"[FFMPEG HEALTH] PID: {config.ffmpeg_process.pid}, CPU: {cpu_percent:.1f}%, Memory: {memory_mb:.1f}MB")
                                    logging.info(f"[FFMPEG HEALTH] Threads: {num_threads} active, {hung_threads} potentially hung, FDs: {open_fds}")
                                    logging.info(f"[FFMPEG HEALTH] Audio FIFO: {fifo_fd_status}")
                                    
                                    # Log thread details if we suspect issues
                                    if hung_threads > 0:
                                        thread_times = [(t['id'], f"{t['user_time']:.2f}u+{t['system_time']:.2f}s") for t in threads_info[:5]]
                                        logging.warning(f"[FFMPEG HEALTH] Thread times: {thread_times}")
                                    
                                    config._last_ffmpeg_health_log = current_time
                        except psutil.NoSuchProcess:
                            logging.error(f"FFmpeg process PID {config.ffmpeg_process.pid} no longer exists")
                            config.ffmpeg_process = None
                            continue
                        except Exception as e:
                            logging.error(f"Error checking FFmpeg process with psutil: {e}")
                            # Don't force restart on psutil errors, just continue
                            pass
                except Exception as e:
                    logging.error(f"Error checking FFmpeg process: {e}")
                    # Force restart
                    config.ffmpeg_process = None
                    continue
            await asyncio.sleep(2)  # Check every 2 seconds for faster FFmpeg death detection
        except asyncio.CancelledError:
            logging.info("FFmpeg monitor cancelled")
            break
        except Exception as e:
            logging.error(f"Error in FFmpeg monitor: {e}")
            await asyncio.sleep(5)


async def memory_monitor():
    """Monitor memory usage and emergency shutdown if limit exceeded"""
    process = psutil.Process(os.getpid())
    last_memory_mb = 0
    
    while True:
        try:
            # Get current memory usage
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024  # Convert to MB
            
            # EMERGENCY SHUTDOWN if memory limit exceeded
            if memory_mb > config.MEMORY_LIMIT_MB:
                logging.critical(f"MEMORY LIMIT EXCEEDED: {memory_mb:.1f}MB > {config.MEMORY_LIMIT_MB}MB - EMERGENCY SHUTDOWN!")
                
                # Cancel any active WebRTC sessions immediately
                if config.current_stream_task:
                    logging.critical("Cancelling active WebRTC session...")
                    config.current_stream_task.cancel()
                    config.current_stream_task = None
                
                # Clear all events and flags
                config.is_live.clear()
                config.is_audio_live.clear()
                
                # Clear all queues aggressively
                try:
                    # Clear video queue
                    while not config.live_frame_queue.empty():
                        config.live_frame_queue.get_nowait()
                        config.live_frame_queue.task_done()
                    
                    # Clear audio queues
                    while not config.live_audio_queue.empty():
                        config.live_audio_queue.get_nowait()
                        config.live_audio_queue.task_done()
                    
                    
                    # Clear event queue
                    while not config.g_event_queue.empty():
                        config.g_event_queue.get_nowait()
                        
                    logging.critical("All queues cleared")
                except Exception as e:
                    logging.critical(f"Error clearing queues: {e}")
                
                # Force multiple garbage collections
                import gc
                for i in range(3):
                    collected = gc.collect()
                    logging.critical(f"GC run {i+1}: collected {collected} objects")
                
                # Terminate processes forcefully
                if config.ffmpeg_process:
                    logging.critical("Terminating FFmpeg process...")
                    config.ffmpeg_process.terminate()
                if config.mediamtx_process:
                    logging.critical("Terminating MediaMTX process...")
                    config.mediamtx_process.terminate()
                
                # Exit immediately
                logging.critical("=" * 80)
                logging.critical("EMERGENCY SHUTDOWN TRIGGERED - MEMORY LIMIT EXCEEDED")
                logging.critical("Application exiting to prevent OOM kill")
                logging.critical("=" * 80)
                os._exit(1)
            
            # Enhanced memory logging for debugging
            if memory_mb > 1000:  # High memory usage - potential leak
                logging.error(f"HIGH MEMORY USAGE: {memory_mb:.1f}MB - investigating potential leak")
                last_memory_mb = memory_mb
            elif memory_mb > 500:  # Elevated memory usage
                logging.warning(f"ELEVATED MEMORY USAGE: {memory_mb:.1f}MB - monitoring closely")
                last_memory_mb = memory_mb
            elif abs(memory_mb - last_memory_mb) > 50:  # Significant change (50MB)
                logging.warning(f"MEMORY CHANGE: {memory_mb:.1f}MB (change: {memory_mb - last_memory_mb:+.1f}MB)")
                last_memory_mb = memory_mb
            elif memory_mb > 200:  # Regular monitoring for moderate usage
                logging.info(f"Memory usage: {memory_mb:.1f}MB")
                last_memory_mb = memory_mb
            
            await asyncio.sleep(5)  # Check every 5 seconds for faster leak detection
        except Exception as e:
            logging.error(f"Memory monitor error: {e}")
            await asyncio.sleep(5)


async def main():
    # Set up logging to both console and file (one file per session)
    import logging.handlers
    import datetime
    import glob
    
    # Create logs directory if it doesn't exist
    import os
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # Clean up old log files, keep only last 5 sessions
    existing_logs = sorted(glob.glob('logs/fermax-onvif-*.log'))
    if len(existing_logs) >= 5:
        # Remove oldest log files, keep 4 (so with new one we'll have 5)
        for old_log in existing_logs[:-4]:
            try:
                os.remove(old_log)
                print(f"Removed old log file: {old_log}")
            except Exception as e:
                print(f"Could not remove old log file {old_log}: {e}")
    
    # Create log file with timestamp for this session
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f'logs/fermax-onvif-{timestamp}.log'
    
    # Set up file logging for this session
    file_handler = logging.FileHandler(log_filename)
    file_handler.setLevel(logging.DEBUG)
    file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_formatter)
    
    # Set up console logging
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(console_formatter)
    
    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    logging.info("=== FERMAX ONVIF APPLICATION STARTING ===")
    logging.info(f"Session log file: {log_filename}")
    logging.info(f"Memory limit: {config.MEMORY_LIMIT_MB}MB")

    # Set up signal handlers for graceful shutdown
    shutdown_event = asyncio.Event()
    
    def signal_handler():
        logging.info("Received shutdown signal, initiating graceful shutdown...")
        shutdown_event.set()
    
    # Register signal handlers
    loop = asyncio.get_running_loop()
    for sig in (signal.SIGTERM, signal.SIGINT):
        loop.add_signal_handler(sig, signal_handler)

    host_ip = get_local_ip()

    try:
        logging.info("Starting MediaMTX server from './mediamtx mediamtx.yml'")
        config.mediamtx_process = await asyncio.create_subprocess_exec(
            './mediamtx', 'mediamtx.yml',
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        asyncio.create_task(log_mediamtx_output(config.mediamtx_process.stdout, logging.info, prefix="[MediaMTX] "))
        asyncio.create_task(log_process_output(config.mediamtx_process.stderr, logging.error, prefix="[MediaMTX-ERROR] "))
        logging.info(f"MediaMTX process started with PID: {config.mediamtx_process.pid}. Waiting a moment for it to initialize...")
        await asyncio.sleep(2)
    except FileNotFoundError:
        logging.error("FATAL: './mediamtx' executable not found. Please ensure it is in the same directory as the script.")
        return

    # Create audio FIFO first
    import tempfile
    import os
    audio_fifo = os.path.join(tempfile.gettempdir(), 'fermax_audio_fifo')
    
    # Clean up any existing FIFO first
    try:
        if os.path.exists(audio_fifo):
            os.unlink(audio_fifo)
    except Exception as e:
        logging.warning(f"Could not remove existing FIFO: {e}")
    
    # Create new FIFO
    try:
        os.mkfifo(audio_fifo)
        logging.info(f"Created audio FIFO at: {audio_fifo}")
    except Exception as e:
        logging.error(f"FATAL: Could not create audio FIFO: {e}")
        return
    
    # Verify FIFO was created successfully
    if not os.path.exists(audio_fifo):
        logging.error("FATAL: Audio FIFO was not created successfully")
        return
    
    # Check if FIFO is accessible
    if not os.access(audio_fifo, os.R_OK | os.W_OK):
        logging.error("FATAL: Audio FIFO is not accessible")
        return
    
    config.audio_fifo_path = audio_fifo
    
    # Start FFmpeg first so it's ready to read from FIFO
    if not await start_ffmpeg(use_software_encoder=False):
        logging.error("Failed to start FFmpeg with hardware encoder, trying software encoder...")
        if not await start_ffmpeg(use_software_encoder=True):
            logging.error("FATAL: Could not start FFmpeg with either hardware or software encoder.")
            return
    
    # Give FFmpeg more time to initialize and be ready to read from FIFO
    await asyncio.sleep(3)
    
    # Wait for MediaMTX to detect the stream before starting generators
    logging.info("Waiting for FFmpeg to establish RTSP connection to MediaMTX...")
    
    # Start generators immediately, they will create the stream that FFmpeg connects to
    pass  # Remove the waiting logic for now

    # Start background tasks
    generator_task = asyncio.create_task(stream_generator())
    audio_generator_task = asyncio.create_task(audio_stream_generator())
    ffmpeg_monitor_task = asyncio.create_task(ffmpeg_monitor())
    memory_monitor_task = asyncio.create_task(memory_monitor())
    buffer_monitor_task = asyncio.create_task(buffer_monitor())
    
    # Add task names for better debugging
    generator_task.set_name("video_stream_generator")
    audio_generator_task.set_name("audio_stream_generator")
    ffmpeg_monitor_task.set_name("ffmpeg_monitor")
    memory_monitor_task.set_name("memory_monitor")
    buffer_monitor_task.set_name("buffer_monitor")

    app = web.Application()
    app.router.add_post('/start', handle_start)
    app.router.add_post('/start_twoway', handle_start_twoway)
    app.router.add_post('/stop', handle_stop)
    app.router.add_post('/onvif/device_service', handle_onvif)
    app.router.add_get('/onvif/snapshot', handle_snapshot)
    app.router.add_post('/press_doorbell', handle_doorbell_press_with_image)
    app.router.add_post('/update_snapshot', handle_update_snapshot)
    app.router.add_post('/audio_input', handle_audio_input)
    app.router.add_get('/audio_diagnostics', handle_audio_diagnostics)

    runner = web.AppRunner(app)
    await runner.setup()
    site = web.TCPSite(runner, '0.0.0.0', config.ONVIF_PORT)
    await site.start()
    logging.info(f"Web server started on http://{host_ip}:{config.ONVIF_PORT}")

    onvif_task = asyncio.create_task(start_onvif_discovery(host_ip))
    shutdown_task = asyncio.create_task(shutdown_event.wait())

    mediamtx_wait_task = asyncio.create_task(config.mediamtx_process.wait())

    done, pending = await asyncio.wait(
        [generator_task, audio_generator_task, ffmpeg_monitor_task, memory_monitor_task, buffer_monitor_task, mediamtx_wait_task, onvif_task, shutdown_task],
        return_when=asyncio.FIRST_COMPLETED
    )
    
    # Log which task completed/failed
    for task in done:
        task_name = getattr(task, '_name', 'unknown_task')
        if task.exception():
            logging.error(f"Critical task '{task_name}' failed with exception: {task.exception()}")
        else:
            logging.info(f"Critical task '{task_name}' completed normally")
    
    for task in pending:
        task_name = getattr(task, '_name', 'unknown_task')
        logging.info(f"Cancelling pending task: {task_name}")
        task.cancel()
    logging.info("A critical process (MediaMTX, Generator, FFmpeg Monitor, or ONVIF) has exited. Shutting down.")
    logging.info("=== FERMAX ONVIF APPLICATION SHUTTING DOWN ===")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logging.info("Process interrupted by user.")
    finally:
        if config.ffmpeg_process and config.ffmpeg_process.returncode is None:
            logging.info("Terminating FFmpeg process...")
            config.ffmpeg_process.terminate()
        if config.mediamtx_process and config.mediamtx_process.returncode is None:
            logging.info("Terminating MediaMTX process...")
            config.mediamtx_process.terminate()
        
        # Clean up audio FIFO
        import os
        if config.audio_fifo_path and os.path.exists(config.audio_fifo_path):
            try:
                os.unlink(config.audio_fifo_path)
                logging.info("Removed audio FIFO")
            except Exception as e:
                logging.warning(f"Could not remove audio FIFO: {e}")
        
        logging.info("Cleanup complete. Exiting.")
        logging.info("=== FERMAX ONVIF APPLICATION ENDED ===")
