import asyncio
import logging
import numpy as np
import struct
import time
from PIL import Image

import config
from config import (
    STILL_IMAGE_PATH, VIDEO_WIDTH, VIDEO_HEIGHT, DUMMY_FPS
)


async def write_to_ffmpeg_stdin_with_timeout(data, timeout=5.0):
    """
    Write data to FFmpeg stdin with timeout detection using asyncio StreamWriter.
    Returns True if successful, False if timeout or error occurred.
    """
    if config.ffmpeg_process is None or config.ffmpeg_process.stdin is None:
        return False
    
    try:
        # Use asyncio.wait_for with the StreamWriter's drain method
        config.ffmpeg_process.stdin.write(data)
        
        # Use drain() with timeout to detect if FFmpeg stops consuming
        await asyncio.wait_for(config.ffmpeg_process.stdin.drain(), timeout=timeout)
        
        # Track success and reset timeout counter on successful write
        config.ffmpeg_stdin_write_success_count += 1
        if config.ffmpeg_stdin_timeout_count > 0:
            logging.info(f"FFmpeg stdin write recovered - resetting timeout counter (was {config.ffmpeg_stdin_timeout_count})")
            config.ffmpeg_stdin_timeout_count = 0
        
        return True
        
    except asyncio.TimeoutError:
        config.ffmpeg_stdin_timeout_count += 1
        config.ffmpeg_stdin_write_failure_count += 1
        logging.error(f"FFmpeg stdin drain timeout after {timeout}s - FFmpeg may have stopped consuming data (timeout #{config.ffmpeg_stdin_timeout_count})")
        
        # Request FFmpeg restart after 3 consecutive timeouts
        if config.ffmpeg_stdin_timeout_count >= 3:
            logging.critical("Multiple FFmpeg stdin timeouts detected - requesting FFmpeg restart")
            config.ffmpeg_restart_requested.set()
        
        return False
    except (BrokenPipeError, ConnectionResetError, OSError) as e:
        config.ffmpeg_stdin_write_failure_count += 1
        logging.error(f"FFmpeg stdin write error: {e}")
        return False
    except Exception as e:
        config.ffmpeg_stdin_write_failure_count += 1
        logging.error(f"Unexpected error writing to FFmpeg stdin: {e}")
        return False


async def write_to_audio_fifo_with_timeout(fifo_handle, data, timeout=5.0, data_type="unknown"):
    """
    Write data to audio FIFO with timeout detection and detailed logging.
    Uses thread executor for regular file I/O operations.
    Returns True if successful, False if timeout or error occurred.
    """
    # Validate and log audio data characteristics
    data_size = len(data)
    
    # Enhanced logging for debugging audio issues
    if not hasattr(config, '_audio_write_count'):
        config._audio_write_count = 0
    config._audio_write_count += 1
    
    # Log detailed info every 1000 writes or on first few writes
    detailed_log = (config._audio_write_count <= 5 or config._audio_write_count % 1000 == 0)
    
    if detailed_log:
        # Analyze audio data characteristics
        if data_size >= 2:
            # Try to interpret as 16-bit samples
            try:
                samples = struct.unpack('<' + 'h' * (data_size // 2), data)
                max_sample = max(abs(s) for s in samples) if samples else 0
                avg_sample = sum(abs(s) for s in samples) / len(samples) if samples else 0
                is_silence = max_sample == 0
                
                logging.info(f"[AUDIO DATA] Write #{config._audio_write_count}: {data_type}, {data_size} bytes, {len(samples)} samples")
                logging.info(f"[AUDIO DATA] Audio levels: max={max_sample}, avg={avg_sample:.1f}, silence={is_silence}")
            except:
                logging.info(f"[AUDIO DATA] Write #{config._audio_write_count}: {data_type}, {data_size} bytes (could not analyze samples)")
        else:
            logging.warning(f"[AUDIO DATA] Write #{config._audio_write_count}: {data_type}, {data_size} bytes (too small for audio)")
    
    try:
        # Audio FIFO uses regular file handle, so we need thread executor
        def blocking_write():
            fifo_handle.write(data)
            fifo_handle.flush()
            return True
        
        # Run the blocking write in a thread with timeout
        result = await asyncio.wait_for(
            asyncio.get_event_loop().run_in_executor(None, blocking_write),
            timeout=timeout
        )
        
        # Track success and reset timeout counter on successful write
        if result:
            config.ffmpeg_audio_fifo_write_success_count += 1
            if config.ffmpeg_stdin_timeout_count > 0:
                logging.info(f"Audio FIFO write recovered - resetting timeout counter (was {config.ffmpeg_stdin_timeout_count})")
                config.ffmpeg_stdin_timeout_count = 0
        
        return result
        
    except asyncio.TimeoutError:
        config.ffmpeg_stdin_timeout_count += 1
        config.ffmpeg_audio_fifo_write_failure_count += 1
        logging.error(f"Audio FIFO write timeout after {timeout}s - FFmpeg may have stopped consuming audio (timeout #{config.ffmpeg_stdin_timeout_count})")
        logging.error(f"[AUDIO TIMEOUT] Failed write: {data_type}, {data_size} bytes")
        
        # Request FFmpeg restart after 3 consecutive timeouts
        if config.ffmpeg_stdin_timeout_count >= 3:
            logging.critical("Multiple audio FIFO timeouts detected - requesting FFmpeg restart")
            config.ffmpeg_restart_requested.set()
        
        return False
    except (BrokenPipeError, OSError) as e:
        config.ffmpeg_audio_fifo_write_failure_count += 1
        # Enhanced error logging for BlockingIOError (Errno 11)
        if hasattr(e, 'errno') and e.errno == 11:
            logging.error(f"Audio FIFO blocked (Errno 11): {data_type}, {data_size} bytes - FFmpeg not consuming audio")
        else:
            logging.error(f"Audio FIFO write error: {e} - {data_type}, {data_size} bytes")
        return False
    except Exception as e:
        config.ffmpeg_audio_fifo_write_failure_count += 1
        logging.error(f"Unexpected error writing to audio FIFO: {e} - {data_type}, {data_size} bytes")
        return False


async def buffer_monitor():
    """Monitor buffer sizes and log warnings if they grow too large"""
    while True:
        try:
            await asyncio.sleep(60)  # Check every minute
            
            # Monitor queue sizes
            video_queue_size = config.live_frame_queue.qsize()
            audio_queue_size = config.live_audio_queue.qsize()
            onvif_audio_size = config.onvif_audio_queue.qsize() if hasattr(config, 'onvif_audio_queue') else 0
            
            # Monitor timeout counter and health stats
            timeout_count = getattr(config, 'ffmpeg_stdin_timeout_count', 0)
            stdin_success = getattr(config, 'ffmpeg_stdin_write_success_count', 0)
            stdin_failure = getattr(config, 'ffmpeg_stdin_write_failure_count', 0)
            audio_success = getattr(config, 'ffmpeg_audio_fifo_write_success_count', 0)
            audio_failure = getattr(config, 'ffmpeg_audio_fifo_write_failure_count', 0)
            
            # Calculate success rates
            stdin_total = stdin_success + stdin_failure
            audio_total = audio_success + audio_failure
            stdin_rate = (stdin_success / stdin_total * 100) if stdin_total > 0 else 100
            audio_rate = (audio_success / audio_total * 100) if audio_total > 0 else 100
            
            # Log warnings if buffers are growing large
            if video_queue_size > 5:
                logging.warning(f"[BUFFER MONITOR] Video queue growing large: {video_queue_size} frames")
            if audio_queue_size > 60:
                logging.warning(f"[BUFFER MONITOR] Audio queue growing large: {audio_queue_size} frames")
            if onvif_audio_size > 100:
                logging.warning(f"[BUFFER MONITOR] ONVIF audio queue growing large: {onvif_audio_size} frames")
            if timeout_count > 0:
                logging.warning(f"[BUFFER MONITOR] FFmpeg stdin timeout count: {timeout_count}")
            if stdin_rate < 95 and stdin_total > 10:
                logging.warning(f"[BUFFER MONITOR] Low stdin write success rate: {stdin_rate:.1f}%")
            if audio_rate < 95 and audio_total > 10:
                logging.warning(f"[BUFFER MONITOR] Low audio FIFO write success rate: {audio_rate:.1f}%")
            
            # Log buffer status periodically (every 10 minutes) or when issues detected
            current_time = time.time()
            if not hasattr(config, '_last_buffer_log'):
                config._last_buffer_log = 0
            
            log_now = (current_time - config._last_buffer_log >= 600 or  # Every 10 minutes
                      video_queue_size > 3 or audio_queue_size > 30 or timeout_count > 0 or
                      stdin_rate < 98 or audio_rate < 98)
            
            if log_now:
                logging.info(f"[BUFFER MONITOR] Queues - Video: {video_queue_size}, Audio: {audio_queue_size}, ONVIF: {onvif_audio_size}")
                logging.info(f"[BUFFER MONITOR] Health - Stdin: {stdin_rate:.1f}% ({stdin_success}/{stdin_total}), Audio: {audio_rate:.1f}% ({audio_success}/{audio_total}), Timeouts: {timeout_count}")
                
                # Audio timing analysis if available
                if hasattr(config, '_audio_write_count') and config._audio_write_count > 0:
                    write_count = config._audio_write_count
                    
                    # Calculate recent audio write performance
                    if hasattr(config, 'ffmpeg_audio_fifo_write_failure_count'):
                        recent_failures = config.ffmpeg_audio_fifo_write_failure_count
                        failure_rate = (recent_failures / write_count * 100) if write_count > 0 else 0
                        
                        logging.info(f"[BUFFER MONITOR] Audio writes: {write_count} total, {failure_rate:.1f}% failure rate")
                        
                        # Warn about audio processing issues
                        if failure_rate > 5:
                            logging.warning(f"[BUFFER MONITOR] High audio write failure rate: {failure_rate:.1f}%")
                        
                config._last_buffer_log = current_time
                
        except asyncio.CancelledError:
            logging.info("Buffer monitor cancelled")
            break
        except Exception as e:
            logging.error(f"Error in buffer monitor: {e}")
            await asyncio.sleep(30)  # Wait before retrying


async def audio_stream_generator():
    """Generate audio stream for FFmpeg - live audio when available, silence otherwise"""
    logging.info("Starting audio stream generator...")

    try:
        while True:
            # Check if audio_fifo_path is available
            if not hasattr(config, 'audio_fifo_path') or config.audio_fifo_path is None:
                await asyncio.sleep(1)
                continue

            # Verify FIFO actually exists
            import os
            if not os.path.exists(config.audio_fifo_path):
                await asyncio.sleep(1)
                continue

            break
    except Exception as e:
        logging.error(f"Audio stream generator initialization error: {e}", exc_info=True)
        return

    # Generate silence frames for fallback (8kHz, mono, 16-bit)
    sample_rate = 8000
    frame_duration = 0.02  # 20ms frames
    samples_per_frame = int(sample_rate * frame_duration)
    silence_frame = struct.pack('<' + 'h' * samples_per_frame, *([0] * samples_per_frame))
    
    # Audio debug tracking
    audio_bytes_written = 0
    audio_last_log_time = 0
    
    # Audio format validation and logging
    expected_frame_size = samples_per_frame * 2  # 16-bit = 2 bytes per sample
    logging.info(f"[AUDIO FORMAT] Expected format: {sample_rate}Hz, 16-bit mono, {frame_duration*1000:.1f}ms frames")
    logging.info(f"[AUDIO FORMAT] Expected frame: {samples_per_frame} samples, {expected_frame_size} bytes")
    logging.info(f"[AUDIO FORMAT] Silence frame generated: {len(silence_frame)} bytes")
    
    # Audio flow tracking
    audio_write_attempts = 0
    audio_write_successes = 0
    audio_write_failures = 0
    last_audio_characteristics = None
    
    # Audio timing analysis
    audio_write_times = []
    last_successful_write_time = time.time()
    consecutive_failures = 0
    fifo_blocked_start_time = None
    
    # Nothing to initialize for balanced approach
    
    # Main retry loop - restart when FIFO connection is broken
    while True:
        try:
            # Wait for FFmpeg to be ready to read from FIFO
            retry_count = 0
            max_retries = 10
            fifo_opened = False
            
            while retry_count < max_retries:
                try:
                    import os
                    
                    # Wait a bit before each attempt to let FFmpeg initialize
                    if retry_count > 0:
                        await asyncio.sleep(1)
                    
                    # Try non-blocking first to see if someone is reading
                    try:
                        fifo_fd = os.open(config.audio_fifo_path, os.O_WRONLY | os.O_NONBLOCK)
                        # Keep it in non-blocking mode to prevent deadlocks
                        import fcntl
                        
                        # Enhanced FIFO status logging
                        fifo_stat = os.stat(config.audio_fifo_path)
                        logging.info(f"[AUDIO FIFO] Opened FIFO successfully, size: {fifo_stat.st_size} bytes")
                        
                        with os.fdopen(fifo_fd, 'wb') as audio_fifo:
                            fifo_opened = True
                            silence_frame_count = 0
                            fifo_health_check_interval = 100  # Check FIFO health every 100 frames (2 seconds)
                            while True:
                                try:
                                    silence_frame_count += 1
                                    
                                    # Always run at full performance
                                    audio_frame_delay = frame_duration  # 20ms frames (50 FPS)
                                    
                                    # Check FIFO health periodically
                                    if silence_frame_count % fifo_health_check_interval == 0:
                                        try:
                                            # Quick FIFO status check
                                            fifo_stat = os.stat(config.audio_fifo_path)
                                            if fifo_stat.st_size > 0:
                                                logging.warning(f"[AUDIO FIFO] FIFO has {fifo_stat.st_size} bytes buffered - FFmpeg may be falling behind")
                                            
                                            # Check if FFmpeg process is still consuming from our FIFO
                                            if config.ffmpeg_process and hasattr(config.ffmpeg_process, 'pid'):
                                                import psutil
                                                try:
                                                    process = psutil.Process(config.ffmpeg_process.pid)
                                                    fifo_fd_found = False
                                                    for fd in process.open_files():
                                                        if config.audio_fifo_path in fd.path:
                                                            fifo_fd_found = True
                                                            break
                                                    
                                                    if not fifo_fd_found:
                                                        logging.error(f"[AUDIO FIFO] FFmpeg no longer has FIFO open - requesting restart")
                                                        config.ffmpeg_restart_requested.set()
                                                        break
                                                except Exception as e:
                                                    logging.debug(f"[AUDIO FIFO] Could not check FFmpeg file descriptors: {e}")
                                        except Exception as e:
                                            logging.debug(f"[AUDIO FIFO] Health check error: {e}")
                                    
                                    # Detailed audio logging every 10 seconds
                                    current_time = time.time()
                                    if current_time - audio_last_log_time >= 10:
                                        live_queue_size = config.live_audio_queue.qsize() if hasattr(config, 'live_audio_queue') and hasattr(config.live_audio_queue, 'qsize') else 'N/A'
                                        is_audio_live = "LIVE" if config.is_audio_live.is_set() else "SILENCE"
                                        mb_written = audio_bytes_written / (1024 * 1024)
                                        logging.info(f"[AUDIO DEBUG] Frame: {silence_frame_count}, Mode: {is_audio_live}, Queue: {live_queue_size}, Data: {mb_written:.1f}MB")
                                        audio_last_log_time = current_time
                                    
                                    if silence_frame_count % 1250 == 0:  # Log every 25 seconds
                                        logging.info(f"Audio generator: processed {silence_frame_count} silence frames")
                                    if config.is_audio_live.is_set():
                                        # Processing live audio frame (logging removed for performance)
                                        try:
                                            # Get live audio frame from queue
                                            audio_data = await config.live_audio_queue.get()
                                            if audio_data is None:
                                                # Signal to switch back to silence
                                                config.live_audio_queue.task_done()
                                                logging.info("Received audio end signal, switching back to silence")
                                                continue
                                            
                                            # Write live audio data to FIFO with timeout protection and timing analysis
                                            write_start_time = time.time()
                                            write_success = await write_to_audio_fifo_with_timeout(audio_fifo, audio_data, timeout=2.0, data_type="live_audio")
                                            write_duration = time.time() - write_start_time
                                            
                                            # Track timing and success patterns
                                            audio_write_times.append((write_start_time, write_duration, write_success))
                                            if len(audio_write_times) > 100:  # Keep last 100 writes
                                                audio_write_times.pop(0)
                                            
                                            if write_success:
                                                audio_bytes_written += len(audio_data)
                                                consecutive_failures = 0
                                                last_successful_write_time = time.time()
                                                if fifo_blocked_start_time:
                                                    blocked_duration = time.time() - fifo_blocked_start_time
                                                    logging.info(f"[AUDIO TIMING] FIFO unblocked after {blocked_duration:.1f}s")
                                                    fifo_blocked_start_time = None
                                            else:
                                                consecutive_failures += 1
                                                if consecutive_failures == 1:
                                                    fifo_blocked_start_time = time.time()
                                                elif consecutive_failures >= 25:  # 25 failures = ~0.5 seconds of blocking
                                                    logging.critical(f"[AUDIO TIMING] {consecutive_failures} consecutive audio write failures - FFmpeg likely hung")
                                                    logging.critical(f"[AUDIO TIMING] Requesting FFmpeg restart due to persistent FIFO blocking")
                                                    config.ffmpeg_restart_requested.set()
                                                    break  # Exit the audio loop to trigger restart
                                                elif consecutive_failures >= 10:
                                                    logging.warning(f"[AUDIO TIMING] {consecutive_failures} consecutive audio write failures")
                                                
                                                logging.debug(f"[AUDIO DEBUG] Failed to write live audio frame to FIFO")
                                                # Don't break the loop, just skip this frame and continue
                                            config.live_audio_queue.task_done()
                                            
                                        except Exception as e:
                                            logging.warning(f"Error processing live audio frame: {e}")
                                            # On error, clear live audio flag and continue with silence
                                            config.is_audio_live.clear()
                                            # Flush the audio queue to prevent buildup
                                            try:
                                                while not config.live_audio_queue.empty():
                                                    config.live_audio_queue.get_nowait()
                                                    config.live_audio_queue.task_done()
                                            except:
                                                pass
                                            continue
                                    else:
                                        # Write silence frame with timeout protection and timing analysis
                                        write_start_time = time.time()
                                        write_success = await write_to_audio_fifo_with_timeout(audio_fifo, silence_frame, timeout=2.0, data_type="silence")
                                        write_duration = time.time() - write_start_time
                                        
                                        # Track timing and success patterns
                                        audio_write_times.append((write_start_time, write_duration, write_success))
                                        if len(audio_write_times) > 100:  # Keep last 100 writes
                                            audio_write_times.pop(0)
                                        
                                        if write_success:
                                            audio_bytes_written += len(silence_frame)
                                            consecutive_failures = 0
                                            last_successful_write_time = time.time()
                                            if fifo_blocked_start_time:
                                                blocked_duration = time.time() - fifo_blocked_start_time
                                                logging.info(f"[AUDIO TIMING] FIFO unblocked after {blocked_duration:.1f}s")
                                                fifo_blocked_start_time = None
                                        else:
                                            consecutive_failures += 1
                                            if consecutive_failures == 1:
                                                fifo_blocked_start_time = time.time()
                                            elif consecutive_failures >= 25:  # 25 failures = ~0.5 seconds of blocking
                                                logging.critical(f"[AUDIO TIMING] {consecutive_failures} consecutive silence write failures - FFmpeg likely hung")
                                                logging.critical(f"[AUDIO TIMING] Requesting FFmpeg restart due to persistent FIFO blocking")
                                                config.ffmpeg_restart_requested.set()
                                                break  # Exit the audio loop to trigger restart
                                            elif consecutive_failures >= 10:
                                                logging.warning(f"[AUDIO TIMING] {consecutive_failures} consecutive silence write failures")
                                            
                                            logging.debug(f"[AUDIO DEBUG] Failed to write silence frame to FIFO")
                                            
                                        await asyncio.sleep(audio_frame_delay)
                                    
                                except Exception as e:
                                    logging.error(f"Error writing to audio FIFO: {e}")
                                    # Connection lost, break to outer loop to retry
                                    raise
                            
                    except (OSError, BrokenPipeError) as e:
                        # FIFO not ready yet, FFmpeg probably hasn't opened it for reading
                        raise  # Re-raise to trigger retry logic
                        
                except (BrokenPipeError, OSError) as e:
                    retry_count += 1
                    if retry_count >= max_retries:
                        logging.error(f"Failed to open audio FIFO after {max_retries} attempts: {e}")
                        break
                except Exception as e:
                    logging.error(f"Unexpected error opening audio FIFO: {e}")
                    break
            
            if not fifo_opened:
                logging.info("Audio FIFO could not be opened - waiting 3s for FFmpeg to restart")
                await asyncio.sleep(3)
            
        except asyncio.CancelledError:
            logging.info("Audio stream generator cancelled")
            break
        except Exception as e:
            logging.error(f"Audio stream generator error: {e}", exc_info=True)
            await asyncio.sleep(3)


async def stream_generator():
    logging.info("Starting stream generator...")
    
    # Load dummy image once
    try:
        dummy_image = Image.open(STILL_IMAGE_PATH).convert("RGB")
        dummy_image = dummy_image.resize((VIDEO_WIDTH, VIDEO_HEIGHT))
        dummy_frame = np.array(dummy_image)
        dummy_frame = dummy_frame[:, :, ::-1]
        dummy_frame_bytes = dummy_frame.tobytes()
    except FileNotFoundError:
        logging.error(f"FATAL: Dummy image not found at '{STILL_IMAGE_PATH}'. Exiting.")
        return

    # Keep track of the current FFmpeg process to detect restarts
    current_ffmpeg = None
    last_was_live = False
    
    frame_count = 0
    bytes_written = 0
    last_log_time = 0
    
    while True:
        try:
            frame_count += 1
            
            # Always run at full performance
            frame_delay = 1 / DUMMY_FPS  # 24 FPS
            
            # Detailed logging every 10 seconds
            current_time = time.time()
            if current_time - last_log_time >= 10:
                queue_size = config.live_frame_queue.qsize() if hasattr(config.live_frame_queue, 'qsize') else 'unknown'
                is_live_status = "LIVE" if config.is_live.is_set() else "DUMMY"
                mb_written = bytes_written / (1024 * 1024)
                logging.info(f"[STREAM DEBUG] Frame: {frame_count}, Mode: {is_live_status}, Queue: {queue_size}, Data: {mb_written:.1f}MB, FFmpeg PID: {config.ffmpeg_process.pid if config.ffmpeg_process else 'None'}")
                last_log_time = current_time
                
            if frame_count % 1200 == 0:  # Log every 50 seconds
                logging.info(f"Stream generator: processed {frame_count} frames")
            
            # Check if FFmpeg process has changed (restarted)
            if config.ffmpeg_process != current_ffmpeg:
                if config.ffmpeg_process is None:
                    logging.info("Waiting for FFmpeg process to be available...")
                    await asyncio.sleep(1)
                    continue
                else:
                    logging.info(f"FFmpeg process changed/restarted (PID: {config.ffmpeg_process.pid}), reconnecting stream generator...")
                    current_ffmpeg = config.ffmpeg_process
                    # Add small delay to let FFmpeg initialize
                    await asyncio.sleep(0.5)
            
            # Additional safety check in case ffmpeg_process becomes None during execution
            if config.ffmpeg_process is None or config.ffmpeg_process.stdin is None:
                logging.warning("FFmpeg process or stdin is None. Waiting for restart...")
                current_ffmpeg = None
                await asyncio.sleep(1)
                continue
                
            if config.is_live.is_set():
                if not last_was_live:
                    logging.info("Stream generator: Switched to live video frames")
                    last_was_live = True
                try:
                    # Check if FFmpeg process is still alive before writing
                    if config.ffmpeg_process.returncode is not None:
                        logging.error("FFmpeg process died while processing live frames, forcing reconnection")
                        current_ffmpeg = None
                        await asyncio.sleep(0.1)
                        continue
                    
                    # Use timeout to avoid getting stuck
                    frame_bytes = await asyncio.wait_for(config.live_frame_queue.get(), timeout=1.0)
                    if frame_bytes is None:
                        # Signal to switch back to dummy frames
                        config.live_frame_queue.task_done()
                        logging.info("Received end signal, switching back to dummy frames")
                        continue
                    frame_size = len(frame_bytes)
                    
                    # Use timeout-based write to detect FFmpeg stdin issues
                    write_success = await write_to_ffmpeg_stdin_with_timeout(frame_bytes, timeout=2.0)
                    if not write_success:
                        logging.warning("Failed to write live frame to FFmpeg stdin - continuing with next frame")
                        config.live_frame_queue.task_done()
                        continue
                    
                    bytes_written += frame_size
                    if frame_count % 240 == 0:  # Log every 10 seconds at 24fps
                        logging.info(f"[STREAM DEBUG] Live frame written: {frame_size} bytes, total: {bytes_written/(1024*1024):.1f}MB")
                    config.live_frame_queue.task_done()
                except asyncio.TimeoutError:
                    # No frame available, write dummy frame to keep stream alive
                    try:
                        # Write frame to FFmpeg with timeout protection
                        frame_size = len(dummy_frame_bytes)
                        
                        write_success = await write_to_ffmpeg_stdin_with_timeout(dummy_frame_bytes, timeout=2.0)
                        if not write_success:
                            logging.warning("Failed to write dummy frame (timeout fallback) to FFmpeg stdin")
                            await asyncio.sleep(frame_delay)
                            continue
                        
                        bytes_written += frame_size
                        if frame_count % 240 == 0:  # Log every 10 seconds at 24fps
                            logging.info(f"[STREAM DEBUG] Dummy frame (timeout fallback) written: {frame_size} bytes, total: {bytes_written/(1024*1024):.1f}MB")
                        await asyncio.sleep(frame_delay)
                    except (BrokenPipeError, ConnectionResetError) as e:
                        logging.warning(f"FFmpeg stdin broken while writing dummy frame: {e}")
                        current_ffmpeg = None  # Force reconnection
                        await asyncio.sleep(0.1)
                        continue
                except Exception as e:
                    logging.warning(f"Error processing live frame: {e}")
                    # On error, clear live flag and continue with dummy frames
                    config.is_live.clear()
                    # Flush the queue to prevent buildup
                    try:
                        while not config.live_frame_queue.empty():
                            config.live_frame_queue.get_nowait()
                            config.live_frame_queue.task_done()
                    except:
                        pass
                    continue
            else:
                if last_was_live:
                    logging.info("Stream generator: Switched to dummy video frames")
                    last_was_live = False
                try:
                    # Check if FFmpeg process is still alive before writing
                    if config.ffmpeg_process.returncode is not None:
                        logging.error("FFmpeg process died, forcing reconnection")
                        current_ffmpeg = None
                        await asyncio.sleep(0.1)
                        continue
                    
                    # Write frame to FFmpeg with timeout protection
                    frame_size = len(dummy_frame_bytes)
                    
                    write_success = await write_to_ffmpeg_stdin_with_timeout(dummy_frame_bytes, timeout=2.0)
                    if not write_success:
                        logging.warning("Failed to write dummy frame to FFmpeg stdin")
                        await asyncio.sleep(frame_delay)
                        continue
                    
                    bytes_written += frame_size
                    if frame_count % 240 == 0:  # Log every 10 seconds at 24fps
                        logging.info(f"[STREAM DEBUG] Dummy frame written: {frame_size} bytes, total: {bytes_written/(1024*1024):.1f}MB")
                    
                    # Frame written successfully
                    await asyncio.sleep(frame_delay)
                except (BrokenPipeError, ConnectionResetError) as e:
                    logging.error(f"FFmpeg stdin broken while writing dummy frames: {e}")
                    logging.info("This suggests FFmpeg process died - monitor should restart it")
                    current_ffmpeg = None  # Force reconnection check
                    await asyncio.sleep(0.1)
                    continue
        except asyncio.CancelledError:
            logging.info("Video stream generator cancelled")
            break
        except (BrokenPipeError, ConnectionResetError) as e:
            logging.error(f"[STREAM DEBUG] FFmpeg process pipe broken: {e}. Total data written: {bytes_written/(1024*1024):.1f}MB")
            break
        except Exception as e:
            logging.error(f"[STREAM DEBUG] Error in stream_generator: {e}. Total data written: {bytes_written/(1024*1024):.1f}MB", exc_info=True)
            break