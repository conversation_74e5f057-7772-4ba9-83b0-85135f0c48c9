rtmp: no
hls: no
webrtc: no
srt: no
rtsp: yes

# Prevent FFmpeg publisher session timeouts that cause memory leaks
readTimeout: 168h
writeTimeout: 168h

paths:
  doorbell:
    source: publisher
    # Allow clients to publish audio back to this path
    publishUser: ""
    publishPass: ""
    publishIps: []
    
  doorbell_audio_input:
    source: publisher
    # This path will receive audio from ONVIF clients
    publishUser: ""
    publishPass: ""
    publishIps: []
