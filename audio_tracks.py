import asyncio
import logging
import wave
import numpy as np
import struct
import math
import time
from aiortc import MediaStreamTrack
from aiortc.mediastreams import AudioFrame
from av import AudioFrame as AVAudioFrame
import config


class FileAudioTrack(MediaStreamTrack):
    """
    Audio track that reads from a WAV file and loops it continuously.
    Converts to PCMA format for transmission to the doorbell.
    """
    kind = "audio"

    def __init__(self, file_path):
        super().__init__()
        self.file_path = file_path
        self.sample_rate = 8000
        self.channels = 1
        self.samples_per_frame = 160  # 20ms frames at 8kHz
        self.frame_duration = 0.02  # 20ms
        self._frame_count = 0

        # Load audio data
        self.audio_data = self._load_audio_file()
        self.audio_position = 0
        self.start_time = None

        logging.info(f"FileAudioTrack initialized with {len(self.audio_data)} samples from {file_path}")

        # DIAGNOSTIC: Basic track creation confirmation (safe version)
        logging.info(f"🔍 DIAGNOSTIC - FileAudioTrack created successfully")
        logging.info(f"🔍 DIAGNOSTIC - Track class: {self.__class__.__name__}")

        # Start monitoring track activity (delayed to avoid initialization issues)
        asyncio.create_task(self._delayed_monitor_start())

    def _load_audio_file(self):
        """Load WAV file and convert to the required format"""
        try:
            with wave.open(self.file_path, 'rb') as wav_file:
                # Get file properties
                frames = wav_file.getnframes()
                sample_width = wav_file.getsampwidth()
                framerate = wav_file.getframerate()
                channels = wav_file.getnchannels()
                
                logging.info(f"Loading audio file: {frames} frames, {sample_width} bytes/sample, "
                           f"{framerate}Hz, {channels} channels")
                
                # Read raw audio data
                raw_data = wav_file.readframes(frames)
                
                # Convert to numpy array
                if sample_width == 1:
                    audio_array = np.frombuffer(raw_data, dtype=np.uint8)
                    # Convert unsigned 8-bit to signed 16-bit
                    audio_array = ((audio_array.astype(np.int16) - 128) * 256)
                elif sample_width == 2:
                    audio_array = np.frombuffer(raw_data, dtype=np.int16)
                else:
                    raise ValueError(f"Unsupported sample width: {sample_width}")
                
                # Handle stereo to mono conversion
                if channels == 2:
                    audio_array = audio_array.reshape(-1, 2)
                    audio_array = np.mean(audio_array, axis=1).astype(np.int16)
                
                # Resample if necessary
                if framerate != self.sample_rate:
                    # Simple resampling - for production use a proper resampler
                    ratio = self.sample_rate / framerate
                    new_length = int(len(audio_array) * ratio)
                    audio_array = np.interp(
                        np.linspace(0, len(audio_array), new_length),
                        np.arange(len(audio_array)),
                        audio_array
                    ).astype(np.int16)
                
                logging.info(f"Processed audio: {len(audio_array)} samples at {self.sample_rate}Hz")
                return audio_array
                
        except Exception as e:
            logging.error(f"Error loading audio file {self.file_path}: {e}")
            # Return silence as fallback
            return np.zeros(self.sample_rate * 5, dtype=np.int16)  # 5 seconds of silence

    async def recv(self):
        """Generate audio frames from the loaded file"""
        # DIAGNOSTIC: Log recv calls
        if self._frame_count % 50 == 0:  # Log every 50 frames (1 second)
            logging.info(f"🔍 DIAGNOSTIC - FileAudioTrack.recv() called: frame {self._frame_count}")
            try:
                ready_state = getattr(self, 'readyState', 'unknown')
                enabled = getattr(self, 'enabled', 'unknown')
                muted = getattr(self, 'muted', 'unknown')
                logging.info(f"🔍 DIAGNOSTIC - Track state: readyState={ready_state}, enabled={enabled}, muted={muted}")
            except Exception as e:
                logging.info(f"🔍 DIAGNOSTIC - Error getting track state: {e}")

        if self.start_time is None:
            self.start_time = asyncio.get_event_loop().time()
            logging.info(f"🔍 DIAGNOSTIC - FileAudioTrack started at time: {self.start_time}")

        # Calculate expected frame time
        frame_time = self.start_time + (self.frame_duration * self._frame_count)
        current_time = asyncio.get_event_loop().time()

        # Wait if we're ahead of schedule
        if current_time < frame_time:
            await asyncio.sleep(frame_time - current_time)

        # Get audio samples for this frame
        frame_samples = self._get_next_frame_samples()

        # DIAGNOSTIC: Log audio data
        if self._frame_count % 50 == 0:
            logging.info(f"🔍 DIAGNOSTIC - Generated {len(frame_samples)} samples, position={self.audio_position}")
            logging.info(f"🔍 DIAGNOSTIC - Sample range: min={np.min(frame_samples)}, max={np.max(frame_samples)}")

        # Keep as int16 format for G.711 PCMA encoder (aiortc expects 's16' format)
        frame_data = frame_samples  # Already np.int16 from _get_next_frame_samples()

        # Create audio frame with s16 format (required by G.711 PCMA encoder)
        frame = AudioFrame.from_ndarray(
            frame_data.reshape(1, -1),  # Shape: (channels, samples)
            format='s16',  # Required by aiortc G.711 encoder
            layout='mono'
        )
        frame.sample_rate = self.sample_rate
        frame.pts = self._frame_count * self.samples_per_frame

        self._frame_count += 1

        # DIAGNOSTIC: Log frame creation
        if self._frame_count % 50 == 0:
            logging.info(f"🔍 DIAGNOSTIC - Created AudioFrame: samples={frame.samples}, rate={frame.sample_rate}")

        return frame

    async def _delayed_monitor_start(self):
        """Start monitoring after a short delay to avoid initialization issues"""
        try:
            # Wait a bit for the track to fully initialize
            await asyncio.sleep(1)

            # Now safely check track attributes
            logging.info(f"🔍 DIAGNOSTIC - FileAudioTrack post-init state check:")
            try:
                ready_state = getattr(self, 'readyState', 'unknown')
                enabled = getattr(self, 'enabled', 'unknown')
                muted = getattr(self, 'muted', 'unknown')
                track_id = getattr(self, 'id', 'unknown')
                logging.info(f"🔍 DIAGNOSTIC - readyState={ready_state}, enabled={enabled}, muted={muted}, id={track_id}")
            except Exception as e:
                logging.info(f"🔍 DIAGNOSTIC - Error getting post-init state: {e}")

            # Start the actual monitoring
            await self._monitor_track_activity()

        except Exception as e:
            logging.error(f"Error in delayed monitor start: {e}")

    async def _monitor_track_activity(self):
        """Monitor track activity and state changes"""
        try:
            logging.info("🔍 DIAGNOSTIC - Starting FileAudioTrack activity monitoring...")

            last_frame_count = 0

            # Monitor for 60 seconds
            for i in range(60):
                await asyncio.sleep(1)

                current_frame_count = self._frame_count
                frames_generated = current_frame_count - last_frame_count
                last_frame_count = current_frame_count

                if i % 10 == 0:  # Log every 10 seconds
                    try:
                        ready_state = getattr(self, 'readyState', 'unknown')
                        enabled = getattr(self, 'enabled', 'unknown')
                        logging.info(f"🔍 DIAGNOSTIC - Track monitoring ({i}s): "
                                   f"total_frames={current_frame_count}, "
                                   f"frames_last_sec={frames_generated}, "
                                   f"readyState={ready_state}, "
                                   f"enabled={enabled}")
                    except Exception as e:
                        logging.info(f"🔍 DIAGNOSTIC - Track monitoring ({i}s): total_frames={current_frame_count}, error={e}")

                if frames_generated == 0 and i > 5:  # No frames for 1 second after 5 second grace period
                    try:
                        ready_state = getattr(self, 'readyState', 'unknown')
                        enabled = getattr(self, 'enabled', 'unknown')
                        logging.warning(f"⚠️ DIAGNOSTIC - Track not generating frames! "
                                      f"readyState={ready_state}, enabled={enabled}")
                    except Exception as e:
                        logging.warning(f"⚠️ DIAGNOSTIC - Track not generating frames! Error getting state: {e}")
                elif frames_generated > 0:
                    if i % 10 == 0:
                        logging.info(f"✅ DIAGNOSTIC - Track actively generating frames: {frames_generated}/sec")

            logging.info("🔍 DIAGNOSTIC - FileAudioTrack monitoring complete")

        except Exception as e:
            logging.error(f"Error in FileAudioTrack monitoring: {e}")

    def _get_next_frame_samples(self):
        """Get the next frame worth of samples, looping if necessary"""
        if len(self.audio_data) == 0:
            # Return silence if no audio data
            return np.zeros(self.samples_per_frame, dtype=np.int16)
        
        # Check if we need to loop
        if self.audio_position + self.samples_per_frame >= len(self.audio_data):
            # We need to loop - take what we can and start from beginning
            remaining = len(self.audio_data) - self.audio_position
            if remaining > 0:
                end_samples = self.audio_data[self.audio_position:self.audio_position + remaining]
            else:
                end_samples = np.array([], dtype=np.int16)
            
            # Reset position and get the rest from the beginning
            self.audio_position = 0
            needed = self.samples_per_frame - len(end_samples)
            if needed > 0:
                start_samples = self.audio_data[self.audio_position:self.audio_position + needed]
                self.audio_position += needed
                frame_samples = np.concatenate([end_samples, start_samples])
            else:
                frame_samples = end_samples
        else:
            # Normal case - just get the next samples
            frame_samples = self.audio_data[self.audio_position:self.audio_position + self.samples_per_frame]
            self.audio_position += self.samples_per_frame
        
        # Ensure we have exactly the right number of samples
        if len(frame_samples) < self.samples_per_frame:
            # Pad with zeros if needed
            padding = np.zeros(self.samples_per_frame - len(frame_samples), dtype=np.int16)
            frame_samples = np.concatenate([frame_samples, padding])
        
        return frame_samples

    def stop(self):
        """Stop the audio track"""
        super().stop()
        logging.info("FileAudioTrack stopped")


class SilenceAudioTrack(MediaStreamTrack):
    """
    Audio track that generates silence.
    Used as a fallback when no audio file is available.
    """
    kind = "audio"

    def __init__(self):
        super().__init__()
        self.sample_rate = 8000
        self.samples_per_frame = 160  # 20ms frames at 8kHz
        self.frame_duration = 0.02  # 20ms
        self._frame_count = 0
        self.start_time = None

        logging.info("SilenceAudioTrack initialized")

    async def recv(self):
        """Generate silence frames"""
        if self.start_time is None:
            self.start_time = asyncio.get_event_loop().time()
        
        # Calculate expected frame time
        frame_time = self.start_time + (self.frame_duration * self._frame_count)
        current_time = asyncio.get_event_loop().time()
        
        # Wait if we're ahead of schedule
        if current_time < frame_time:
            await asyncio.sleep(frame_time - current_time)
        
        # Create silence frame with s16 format (required by G.711 PCMA encoder)
        silence_data = np.zeros(self.samples_per_frame, dtype=np.int16)

        frame = AudioFrame.from_ndarray(
            silence_data.reshape(1, -1),  # Shape: (channels, samples)
            format='s16',  # Required by aiortc G.711 encoder
            layout='mono'
        )
        frame.sample_rate = self.sample_rate
        frame.pts = self._frame_count * self.samples_per_frame
        
        self._frame_count += 1
        return frame

    def stop(self):
        """Stop the audio track"""
        super().stop()
        logging.info("SilenceAudioTrack stopped")


class ToneAudioTrack(MediaStreamTrack):
    """
    Audio track that generates pure sine wave tones.
    Used for testing audio transmission with predictable content.
    """
    kind = "audio"

    def __init__(self, frequency=440.0, amplitude=0.5):
        super().__init__()
        self.frequency = frequency  # Hz
        self.amplitude = amplitude  # 0.0 to 1.0
        self.sample_rate = 8000
        self.samples_per_frame = 160  # 20ms frames at 8kHz
        self.frame_duration = 0.02  # 20ms
        self._frame_count = 0
        self.start_time = None
        self.phase = 0.0  # Current phase for continuous tone
        
        logging.info(f"ToneAudioTrack initialized: {frequency}Hz tone at {amplitude*100:.1f}% amplitude")

    async def recv(self):
        """Generate pure tone frames"""
        if self.start_time is None:
            self.start_time = asyncio.get_event_loop().time()
        
        # Calculate expected frame time for precise timing
        frame_time = self.start_time + (self.frame_duration * self._frame_count)
        current_time = asyncio.get_event_loop().time()
        sleep_time = frame_time - current_time
        
        if sleep_time > 0:
            await asyncio.sleep(sleep_time)
        
        # Generate sine wave samples
        samples = np.zeros(self.samples_per_frame, dtype=np.int16)
        for i in range(self.samples_per_frame):
            # Calculate sine wave value
            sample_time = (self._frame_count * self.samples_per_frame + i) / self.sample_rate
            sine_value = math.sin(2 * math.pi * self.frequency * sample_time + self.phase)
            
            # Convert to 16-bit PCM
            samples[i] = int(sine_value * self.amplitude * 32767)
        
        # Create audio frame
        frame = AudioFrame.from_ndarray(
            samples.reshape(1, -1),
            format='s16',
            layout='mono'
        )
        frame.sample_rate = self.sample_rate
        frame.pts = self._frame_count * self.samples_per_frame
        
        self._frame_count += 1
        
        # Diagnostic logging (less frequent for tones)
        if self._frame_count % 50 == 0:  # Every 50 frames (1 second)
            logging.info(f"🔍 DIAGNOSTIC - ToneAudioTrack generated {self._frame_count} frames "
                        f"({self.frequency}Hz tone)")
        
        return frame

    def stop(self):
        """Stop the tone generator"""
        super().stop()
        logging.info("ToneAudioTrack stopped")


class VaryingToneAudioTrack(MediaStreamTrack):
    """
    Audio track that generates varying frequency tones to avoid noise cancellation.
    Changes frequency every 2 seconds to bypass AGC/noise suppression.
    """
    kind = "audio"

    def __init__(self, base_frequency=440.0, amplitude=0.5):
        super().__init__()
        self.base_frequency = base_frequency
        self.amplitude = amplitude
        self.sample_rate = 8000
        self.samples_per_frame = 160  # 20ms frames at 8kHz
        self.frame_duration = 0.02  # 20ms
        self._frame_count = 0
        self.start_time = None
        
        # Frequency pattern: 440Hz -> 880Hz -> 220Hz -> 660Hz (cycle every 8 seconds)
        self.frequency_pattern = [440.0, 880.0, 220.0, 660.0]
        self.pattern_duration = 2.0  # seconds per frequency
        
        logging.info(f"VaryingToneAudioTrack initialized: base {base_frequency}Hz, varying pattern")

    async def recv(self):
        """Generate varying frequency tone frames"""
        if self.start_time is None:
            self.start_time = asyncio.get_event_loop().time()
        
        # Calculate expected frame time for precise timing
        frame_time = self.start_time + (self.frame_duration * self._frame_count)
        current_time = asyncio.get_event_loop().time()
        sleep_time = frame_time - current_time
        
        if sleep_time > 0:
            await asyncio.sleep(sleep_time)
        
        # Calculate current frequency based on time
        elapsed_time = current_time - self.start_time
        pattern_index = int((elapsed_time / self.pattern_duration) % len(self.frequency_pattern))
        current_frequency = self.frequency_pattern[pattern_index]
        
        # Generate sine wave samples
        samples = np.zeros(self.samples_per_frame, dtype=np.int16)
        for i in range(self.samples_per_frame):
            sample_time = (self._frame_count * self.samples_per_frame + i) / self.sample_rate
            sine_value = math.sin(2 * math.pi * current_frequency * sample_time)
            samples[i] = int(sine_value * self.amplitude * 32767)
        
        # Create audio frame
        frame = AudioFrame.from_ndarray(
            samples.reshape(1, -1),
            format='s16',
            layout='mono'
        )
        frame.sample_rate = self.sample_rate
        frame.pts = self._frame_count * self.samples_per_frame
        
        self._frame_count += 1
        
        # Diagnostic logging
        if self._frame_count % 100 == 0:  # Every 100 frames (2 seconds)
            logging.info(f"🔍 DIAGNOSTIC - VaryingToneAudioTrack generated {self._frame_count} frames "
                        f"(current freq: {current_frequency}Hz)")
        
        return frame

    def stop(self):
        """Stop the varying tone generator"""
        super().stop()
        logging.info("VaryingToneAudioTrack stopped")


class SpeechLikeAudioTrack(MediaStreamTrack):
    """
    Audio track that generates speech-like patterns to bypass Voice Activity Detection.
    Creates bursts of varied frequencies with pauses, mimicking human speech patterns.
    """
    kind = "audio"

    def __init__(self, amplitude=0.5):
        super().__init__()
        self.amplitude = amplitude
        self.sample_rate = 8000
        self.samples_per_frame = 160  # 20ms frames at 8kHz
        self.frame_duration = 0.02  # 20ms
        self._frame_count = 0
        self.start_time = None
        
        # Speech-like pattern: talk for 1s, pause 0.5s, repeat
        self.talk_duration = 1.0  # seconds of "speech"
        self.pause_duration = 0.5  # seconds of silence
        self.cycle_duration = self.talk_duration + self.pause_duration
        
        # Frequencies that sound more speech-like (formant-like)
        self.speech_frequencies = [300, 800, 1200, 400, 600, 1000]  # Hz
        
        logging.info(f"SpeechLikeAudioTrack initialized: speech-like patterns with pauses")

    async def recv(self):
        """Generate speech-like audio frames"""
        if self.start_time is None:
            self.start_time = asyncio.get_event_loop().time()
        
        # Calculate expected frame time for precise timing
        frame_time = self.start_time + (self.frame_duration * self._frame_count)
        current_time = asyncio.get_event_loop().time()
        sleep_time = frame_time - current_time
        
        if sleep_time > 0:
            await asyncio.sleep(sleep_time)
        
        # Calculate current position in speech cycle
        elapsed_time = current_time - self.start_time
        cycle_position = elapsed_time % self.cycle_duration
        
        # Determine if we're in "talking" or "pausing" phase
        is_talking = cycle_position < self.talk_duration
        
        samples = np.zeros(self.samples_per_frame, dtype=np.int16)
        
        if is_talking:
            # Generate complex waveform mixing multiple frequencies (like speech formants)
            talk_position = cycle_position / self.talk_duration
            freq_index = int(talk_position * len(self.speech_frequencies)) % len(self.speech_frequencies)
            primary_freq = self.speech_frequencies[freq_index]
            secondary_freq = self.speech_frequencies[(freq_index + 1) % len(self.speech_frequencies)]
            
            for i in range(self.samples_per_frame):
                sample_time = (self._frame_count * self.samples_per_frame + i) / self.sample_rate
                
                # Mix two frequencies with varying amplitude (like speech)
                primary_wave = math.sin(2 * math.pi * primary_freq * sample_time)
                secondary_wave = math.sin(2 * math.pi * secondary_freq * sample_time) * 0.3
                
                # Add some "roughness" to make it more speech-like
                roughness = math.sin(2 * math.pi * 50 * sample_time) * 0.1
                
                combined_wave = primary_wave + secondary_wave + roughness
                samples[i] = int(combined_wave * self.amplitude * 32767 * 0.7)  # Slightly quieter
        # else: leave samples as zeros (silence during pause)
        
        # Create audio frame
        frame = AudioFrame.from_ndarray(
            samples.reshape(1, -1),
            format='s16',
            layout='mono'
        )
        frame.sample_rate = self.sample_rate
        frame.pts = self._frame_count * self.samples_per_frame
        
        self._frame_count += 1
        
        # Diagnostic logging
        if self._frame_count % 75 == 0:  # Every 75 frames (1.5 seconds)
            phase = "talking" if is_talking else "pausing"
            logging.info(f"🔍 DIAGNOSTIC - SpeechLikeAudioTrack generated {self._frame_count} frames "
                        f"(phase: {phase})")
        
        return frame

    def stop(self):
        """Stop the speech-like audio generator"""
        super().stop()
        logging.info("SpeechLikeAudioTrack stopped")


class ONVIFAudioTrack(MediaStreamTrack):
    """
    Audio track that receives real-time audio from ONVIF clients via the Go RTSP proxy.
    Processes audio from the onvif_audio_queue and converts it for WebRTC transmission.
    """
    kind = "audio"

    def __init__(self):
        super().__init__()
        self.sample_rate = 8000
        self.channels = 1
        self.samples_per_frame = 160  # 20ms frames at 8kHz
        self.frame_duration = 0.02  # 20ms
        self._frame_count = 0
        self.start_time = None
        
        # Audio buffer management
        self.audio_buffer = bytearray()
        self.buffer_lock = asyncio.Lock()
        self.last_audio_time = None
        self.silence_threshold = 1.0  # seconds of no audio before generating silence
        
        logging.info("ONVIFAudioTrack initialized - ready for real-time ONVIF audio")

    async def recv(self):
        """Generate audio frames from ONVIF audio queue or silence if no audio available"""
        if self.start_time is None:
            self.start_time = asyncio.get_event_loop().time()
            logging.info("🎤 ONVIFAudioTrack started - listening for ONVIF audio")

        # Calculate expected frame time for precise timing
        frame_time = self.start_time + (self.frame_duration * self._frame_count)
        current_time = asyncio.get_event_loop().time()

        # Wait if we're ahead of schedule
        if current_time < frame_time:
            await asyncio.sleep(frame_time - current_time)

        # Try to get audio data from ONVIF queue
        audio_data = await self._get_audio_from_queue()
        
        if audio_data is not None:
            self.last_audio_time = current_time
            
            # Convert G.711 PCMA to 16-bit PCM if needed
            frame_samples = self._process_audio_data(audio_data)
            
            # Log audio reception
            if self._frame_count % 50 == 0:  # Every 50 frames (1 second)
                logging.info(f"🎤 ONVIF audio frame {self._frame_count}: {len(frame_samples)} samples from ONVIF client")
        else:
            # No audio available - check if we should generate silence
            if (self.last_audio_time is None or 
                (current_time - self.last_audio_time) > self.silence_threshold):
                # Generate silence
                frame_samples = np.zeros(self.samples_per_frame, dtype=np.int16)
                
                if self._frame_count % 200 == 0:  # Every 200 frames (4 seconds)
                    logging.info(f"🔇 ONVIF audio frame {self._frame_count}: generating silence (no ONVIF audio)")
            else:
                # Recently had audio, keep generating silence briefly
                frame_samples = np.zeros(self.samples_per_frame, dtype=np.int16)

        # Create audio frame with s16 format (required by G.711 PCMA encoder)
        frame = AudioFrame.from_ndarray(
            frame_samples.reshape(1, -1),  # Shape: (channels, samples)
            format='s16',  # Required by aiortc G.711 encoder
            layout='mono'
        )
        frame.sample_rate = self.sample_rate
        frame.pts = self._frame_count * self.samples_per_frame

        self._frame_count += 1
        return frame

    async def _get_audio_from_queue(self):
        """Get audio data from the ONVIF audio queue (non-blocking)"""
        try:
            # Use get_nowait to avoid blocking the audio stream
            if not config.onvif_audio_queue.empty():
                audio_item = config.onvif_audio_queue.get_nowait()
                
                # Extract audio data from the queued item
                if isinstance(audio_item, dict):
                    return audio_item.get('data', None)
                else:
                    # Assume it's raw audio data
                    return audio_item
            else:
                return None
                
        except asyncio.QueueEmpty:
            return None
        except Exception as e:
            logging.error(f"Error getting audio from ONVIF queue: {e}")
            return None

    def _process_audio_data(self, audio_data):
        """Process audio data from ONVIF clients (G.711 PCMA format)"""
        try:
            # ONVIF clients typically send G.711 PCMA (A-law) audio
            # Convert from bytes to numpy array
            if isinstance(audio_data, (bytes, bytearray)):
                # If it's G.711 A-law data, we need to decode it to PCM
                pcm_samples = self._decode_g711_alaw(audio_data)
            else:
                # Assume it's already PCM data
                pcm_samples = np.frombuffer(audio_data, dtype=np.int16)
            
            # Ensure we have exactly the right number of samples
            if len(pcm_samples) > self.samples_per_frame:
                # Too many samples - take the first samples_per_frame
                return pcm_samples[:self.samples_per_frame]
            elif len(pcm_samples) < self.samples_per_frame:
                # Too few samples - pad with zeros
                padded = np.zeros(self.samples_per_frame, dtype=np.int16)
                padded[:len(pcm_samples)] = pcm_samples
                return padded
            else:
                # Exact match
                return pcm_samples
                
        except Exception as e:
            logging.error(f"Error processing ONVIF audio data: {e}")
            # Return silence on error
            return np.zeros(self.samples_per_frame, dtype=np.int16)

    def _decode_g711_alaw(self, alaw_data):
        """Decode G.711 A-law data to 16-bit PCM"""
        # G.711 A-law to PCM conversion lookup table
        alaw_to_pcm = np.array([
            -5504, -5248, -6016, -5760, -4480, -4224, -4992, -4736,
            -7552, -7296, -8064, -7808, -6528, -6272, -7040, -6784,
            -2752, -2624, -3008, -2880, -2240, -2112, -2496, -2368,
            -3776, -3648, -4032, -3904, -3264, -3136, -3520, -3392,
            -22016, -20992, -24064, -23040, -17920, -16896, -19968, -18944,
            -30208, -29184, -32256, -31232, -26112, -25088, -28160, -27136,
            -11008, -10496, -12032, -11520, -8960, -8448, -9984, -9472,
            -15104, -14592, -16128, -15616, -13056, -12544, -14080, -13568,
            -344, -328, -376, -360, -280, -264, -312, -296,
            -472, -456, -504, -488, -408, -392, -440, -424,
            -88, -72, -120, -104, -24, -8, -56, -40,
            -216, -200, -248, -232, -152, -136, -184, -168,
            -1376, -1312, -1504, -1440, -1120, -1056, -1248, -1184,
            -1888, -1824, -2016, -1952, -1632, -1568, -1760, -1696,
            -688, -656, -752, -720, -560, -528, -624, -592,
            -944, -912, -1008, -976, -816, -784, -880, -848,
            5504, 5248, 6016, 5760, 4480, 4224, 4992, 4736,
            7552, 7296, 8064, 7808, 6528, 6272, 7040, 6784,
            2752, 2624, 3008, 2880, 2240, 2112, 2496, 2368,
            3776, 3648, 4032, 3904, 3264, 3136, 3520, 3392,
            22016, 20992, 24064, 23040, 17920, 16896, 19968, 18944,
            30208, 29184, 32256, 31232, 26112, 25088, 28160, 27136,
            11008, 10496, 12032, 11520, 8960, 8448, 9984, 9472,
            15104, 14592, 16128, 15616, 13056, 12544, 14080, 13568,
            344, 328, 376, 360, 280, 264, 312, 296,
            472, 456, 504, 488, 408, 392, 440, 424,
            88, 72, 120, 104, 24, 8, 56, 40,
            216, 200, 248, 232, 152, 136, 184, 168,
            1376, 1312, 1504, 1440, 1120, 1056, 1248, 1184,
            1888, 1824, 2016, 1952, 1632, 1568, 1760, 1696,
            688, 656, 752, 720, 560, 528, 624, 592,
            944, 912, 1008, 976, 816, 784, 880, 848
        ], dtype=np.int16)
        
        # Convert each A-law byte to PCM
        alaw_bytes = np.frombuffer(alaw_data, dtype=np.uint8)
        pcm_samples = alaw_to_pcm[alaw_bytes]
        
        return pcm_samples

    def stop(self):
        """Stop the ONVIF audio track"""
        super().stop()
        logging.info("ONVIFAudioTrack stopped")


def create_audio_track(file_path=None, track_type="file"):
    """
    Factory function to create an appropriate audio track.
    
    Args:
        file_path: Path to WAV file (for file type)
        track_type: "file", "tone", "varying_tone", "speech_like", "silence", or "onvif"
        
    Returns:
        MediaStreamTrack: Audio track instance
    """
    if track_type == "onvif":
        # Create ONVIF real-time audio track
        return ONVIFAudioTrack()
    elif track_type == "tone":
        # Create a 440Hz tone (A4 note) - easily audible test signal
        return ToneAudioTrack(frequency=440.0, amplitude=0.7)
    elif track_type == "tone_low":
        # Create a 220Hz tone (A3 note) - lower frequency test
        return ToneAudioTrack(frequency=220.0, amplitude=0.7)
    elif track_type == "tone_high":
        # Create a 880Hz tone (A5 note) - higher frequency test
        return ToneAudioTrack(frequency=880.0, amplitude=0.7)
    elif track_type == "varying_tone":
        # Create varying frequency tones to bypass noise cancellation
        return VaryingToneAudioTrack(base_frequency=440.0, amplitude=0.7)
    elif track_type == "speech_like":
        # Create speech-like patterns to bypass Voice Activity Detection
        return SpeechLikeAudioTrack(amplitude=0.7)
    elif file_path:
        try:
            return FileAudioTrack(file_path)
        except Exception as e:
            logging.error(f"Failed to create FileAudioTrack: {e}, falling back to speech-like")
            return SpeechLikeAudioTrack(amplitude=0.7)
    else:
        return SilenceAudioTrack()
